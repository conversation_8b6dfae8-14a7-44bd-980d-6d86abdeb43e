#!/usr/bin/env python3
"""
Alpha News数据处理脚本

此脚本处理alpha_news目录中的所有JSONL文件，提取指定字段并转换为JSON格式。
输出文件命名格式：alpha_news_YYYY-MM-DD.json

作者：AI助手
日期：2025-06-17
"""

import json
import os
import glob
from datetime import datetime
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_alpha_news.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 需要提取的字段列表
REQUIRED_FIELDS = [
    'title',
    'url', 
    'time_published',
    'authors',
    'summary',
    'overall_sentiment_score',
    'overall_sentiment_label'
]

def extract_fields(news_item: Dict[str, Any]) -> Dict[str, Any]:
    """
    从新闻条目中提取指定字段
    
    Args:
        news_item: 原始新闻数据字典
        
    Returns:
        包含指定字段的字典
    """
    extracted = {}
    
    for field in REQUIRED_FIELDS:
        if field in news_item:
            extracted[field] = news_item[field]
        else:
            logger.warning(f"字段 '{field}' 在新闻条目中不存在")
            extracted[field] = None
            
    return extracted

def process_jsonl_file(file_path: str) -> List[Dict[str, Any]]:
    """
    处理单个JSONL文件
    
    Args:
        file_path: JSONL文件路径
        
    Returns:
        处理后的新闻条目列表
    """
    processed_items = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                    
                try:
                    news_item = json.loads(line)
                    extracted_item = extract_fields(news_item)
                    processed_items.append(extracted_item)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"文件 {file_path} 第 {line_num} 行JSON解析错误: {e}")
                    continue
                    
    except FileNotFoundError:
        logger.error(f"文件不存在: {file_path}")
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时发生错误: {e}")
        
    return processed_items

def extract_date_from_filename(filename: str) -> str:
    """
    从文件名中提取日期
    
    Args:
        filename: 文件名 (例如: "2025-02-06.jsonl")
        
    Returns:
        日期字符串 (例如: "2025-02-06")
    """
    basename = os.path.basename(filename)
    date_part = basename.replace('.jsonl', '')
    return date_part

def save_json_file(data: List[Dict[str, Any]], output_path: str) -> bool:
    """
    保存数据为JSON文件
    
    Args:
        data: 要保存的数据列表
        output_path: 输出文件路径
        
    Returns:
        保存是否成功
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)
        logger.info(f"成功保存文件: {output_path} (包含 {len(data)} 条记录)")
        return True
    except Exception as e:
        logger.error(f"保存文件 {output_path} 时发生错误: {e}")
        return False

def main():
    """
    主函数：处理所有JSONL文件并生成JSON输出
    """
    logger.info("开始处理Alpha News数据...")
    
    # 输入和输出目录
    input_dir = "alpha_news"
    output_dir = "MSFT_alpha_news"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有JSONL文件
    jsonl_pattern = os.path.join(input_dir, "*.jsonl")
    jsonl_files = glob.glob(jsonl_pattern)
    
    if not jsonl_files:
        logger.error(f"在目录 {input_dir} 中未找到JSONL文件")
        return
    
    logger.info(f"找到 {len(jsonl_files)} 个JSONL文件")
    
    # 统计信息
    total_processed = 0
    successful_files = 0
    failed_files = 0
    
    # 处理每个文件
    for jsonl_file in sorted(jsonl_files):
        logger.info(f"正在处理: {jsonl_file}")
        
        # 提取日期
        date_str = extract_date_from_filename(jsonl_file)
        
        # 处理文件
        processed_data = process_jsonl_file(jsonl_file)
        
        if processed_data:
            # 生成输出文件名
            output_filename = f"alpha_news_{date_str}.json"
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存处理后的数据
            if save_json_file(processed_data, output_path):
                successful_files += 1
                total_processed += len(processed_data)
            else:
                failed_files += 1
        else:
            logger.warning(f"文件 {jsonl_file} 没有有效数据")
            failed_files += 1
    
    # 输出统计信息
    logger.info("=" * 50)
    logger.info("处理完成！统计信息：")
    logger.info(f"总文件数: {len(jsonl_files)}")
    logger.info(f"成功处理: {successful_files}")
    logger.info(f"处理失败: {failed_files}")
    logger.info(f"总新闻条目: {total_processed}")
    logger.info(f"输出目录: {output_dir}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
